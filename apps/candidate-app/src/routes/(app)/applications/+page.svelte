<script lang="ts">
  import { Calendar, Building2, MapPin, Clock, Eye } from 'lucide-svelte'
  
  // Mock application data
  const applications = [
    {
      id: 1,
      jobTitle: 'Senior Software Engineer',
      company: 'TechCorp Inc.',
      location: 'San Francisco, CA',
      appliedDate: '2024-06-15',
      status: 'interview_scheduled',
      statusText: 'Interview Scheduled',
      nextStep: 'Technical interview on June 20th',
      salary: '$120k - $160k',
      type: 'Full-time'
    },
    {
      id: 2,
      jobTitle: 'Full Stack Developer',
      company: 'StartupXYZ',
      location: 'Austin, TX',
      appliedDate: '2024-06-10',
      status: 'under_review',
      statusText: 'Under Review',
      nextStep: 'Waiting for hiring manager review',
      salary: '$90k - $120k',
      type: 'Full-time'
    },
    {
      id: 3,
      jobTitle: 'Frontend Developer',
      company: 'BigTech Solutions',
      location: 'Remote',
      appliedDate: '2024-06-08',
      status: 'applied',
      statusText: 'Application Sent',
      nextStep: 'Application received, no updates yet',
      salary: '$85k - $110k',
      type: 'Contract'
    },
    {
      id: 4,
      jobTitle: 'DevOps Engineer',
      company: 'CloudServe',
      location: 'Seattle, WA',
      appliedDate: '2024-06-05',
      status: 'rejected',
      statusText: 'Not Selected',
      nextStep: 'Position filled by another candidate',
      salary: '$100k - $140k',
      type: 'Full-time'
    },
    {
      id: 5,
      jobTitle: 'React Developer',
      company: 'InnovateLabs',
      location: 'Remote',
      appliedDate: '2024-06-01',
      status: 'offer',
      statusText: 'Offer Received',
      nextStep: 'Review offer details and respond by June 25th',
      salary: '$95k - $125k',
      type: 'Full-time'
    }
  ]
  
  function getStatusColor(status: string) {
    switch (status) {
      case 'interview_scheduled':
        return 'bg-blue-100 text-blue-800'
      case 'under_review':
        return 'bg-yellow-100 text-yellow-800'
      case 'applied':
        return 'bg-gray-100 text-gray-800'
      case 'rejected':
        return 'bg-red-100 text-red-800'
      case 'offer':
        return 'bg-green-100 text-green-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }
  
  function formatDate(dateString: string) {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }
  
  let selectedStatus = $state('all')
  
  let filteredApplications = $derived(
    selectedStatus === 'all' 
      ? applications 
      : applications.filter(app => app.status === selectedStatus)
  )
</script>

<svelte:head>
  <title>My Applications - Candidate Portal</title>
</svelte:head>

<div class="space-y-6">
  <!-- Header -->
  <div class="flex items-center justify-between">
    <div>
      <h1 class="text-2xl font-bold text-gray-900">My Applications</h1>
      <p class="text-gray-600">Track the status of your job applications</p>
    </div>
    <div class="text-right">
      <p class="text-sm text-gray-500">Total Applications</p>
      <p class="text-2xl font-bold text-gray-900">{applications.length}</p>
    </div>
  </div>

  <!-- Status Filter -->
  <div class="bg-white rounded-lg border border-gray-200 p-3 sm:p-4">
    <div class="flex flex-wrap gap-2">
      <button
        onclick={() => selectedStatus = 'all'}
        class="px-3 py-1 rounded-full text-sm font-medium transition-colors {selectedStatus === 'all' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}"
      >
        All ({applications.length})
      </button>
      <button
        onclick={() => selectedStatus = 'applied'}
        class="px-3 py-1 rounded-full text-sm font-medium transition-colors {selectedStatus === 'applied' ? 'bg-gray-100 text-gray-800' : 'bg-gray-50 text-gray-600 hover:bg-gray-100'}"
      >
        Applied ({applications.filter(a => a.status === 'applied').length})
      </button>
      <button
        onclick={() => selectedStatus = 'under_review'}
        class="px-3 py-1 rounded-full text-sm font-medium transition-colors {selectedStatus === 'under_review' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-50 text-gray-600 hover:bg-gray-100'}"
      >
        Under Review ({applications.filter(a => a.status === 'under_review').length})
      </button>
      <button
        onclick={() => selectedStatus = 'interview_scheduled'}
        class="px-3 py-1 rounded-full text-sm font-medium transition-colors {selectedStatus === 'interview_scheduled' ? 'bg-blue-100 text-blue-800' : 'bg-gray-50 text-gray-600 hover:bg-gray-100'}"
      >
        Interviews ({applications.filter(a => a.status === 'interview_scheduled').length})
      </button>
      <button
        onclick={() => selectedStatus = 'offer'}
        class="px-3 py-1 rounded-full text-sm font-medium transition-colors {selectedStatus === 'offer' ? 'bg-green-100 text-green-800' : 'bg-gray-50 text-gray-600 hover:bg-gray-100'}"
      >
        Offers ({applications.filter(a => a.status === 'offer').length})
      </button>
      <button
        onclick={() => selectedStatus = 'rejected'}
        class="px-3 py-1 rounded-full text-sm font-medium transition-colors {selectedStatus === 'rejected' ? 'bg-red-100 text-red-800' : 'bg-gray-50 text-gray-600 hover:bg-gray-100'}"
      >
        Closed ({applications.filter(a => a.status === 'rejected').length})
      </button>
    </div>
  </div>

  <!-- Applications List -->
  <div class="space-y-4">
    {#each filteredApplications as application}
      <div class="bg-white rounded-lg border border-gray-200 p-4 sm:p-6 hover:border-blue-300 transition-colors">
        <div class="flex items-start justify-between">
          <div class="flex-1">
            <div class="flex items-center gap-3 mb-2">
              <h3 class="text-lg font-semibold text-gray-900">{application.jobTitle}</h3>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {getStatusColor(application.status)}">
                {application.statusText}
              </span>
            </div>
            
            <div class="flex items-center gap-4 text-sm text-gray-600 mb-3">
              <div class="flex items-center gap-1">
                <Building2 class="w-4 h-4" />
                {application.company}
              </div>
              <div class="flex items-center gap-1">
                <MapPin class="w-4 h-4" />
                {application.location}
              </div>
              <div class="flex items-center gap-1">
                <Clock class="w-4 h-4" />
                {application.type}
              </div>
              <div class="flex items-center gap-1">
                <Calendar class="w-4 h-4" />
                Applied {formatDate(application.appliedDate)}
              </div>
            </div>
            
            <div class="bg-gray-50 rounded-lg p-3 mb-3">
              <p class="text-sm text-gray-700">
                <span class="font-medium">Next Step:</span> {application.nextStep}
              </p>
            </div>
            
            <p class="text-sm text-gray-600">Salary: {application.salary}</p>
          </div>
          
          <div class="ml-4 sm:ml-6 flex flex-col gap-2">
            <button class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors text-sm font-medium flex items-center gap-2">
              <Eye class="w-4 h-4" />
              View Details
            </button>
            {#if application.status === 'offer'}
              <button class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors text-sm font-medium">
                Review Offer
              </button>
            {:else if application.status === 'interview_scheduled'}
              <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm font-medium">
                Interview Details
              </button>
            {/if}
          </div>
        </div>
      </div>
    {/each}
  </div>

  {#if filteredApplications.length === 0}
    <div class="text-center py-12">
      <Building2 class="mx-auto h-12 w-12 text-gray-400" />
      <h3 class="mt-2 text-sm font-medium text-gray-900">No applications found</h3>
      <p class="mt-1 text-sm text-gray-500">
        {selectedStatus === 'all' ? 'You haven\'t applied to any jobs yet' : `No applications with status "${selectedStatus}"`}
      </p>
      {#if selectedStatus === 'all'}
        <div class="mt-6">
          <a href="/jobs" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
            Browse Jobs
          </a>
        </div>
      {/if}
    </div>
  {/if}
</div>
