<script lang="ts">
  import { Search, MapPin, DollarSign, Clock, Building2 } from 'lucide-svelte'
  
  let searchQuery = $state('')
  let locationFilter = $state('')
  let salaryFilter = $state('')
  
  // Mock job data
  const jobs = [
    {
      id: 1,
      title: 'Senior Software Engineer',
      company: 'TechCorp Inc.',
      location: 'San Francisco, CA',
      salary: '$120k - $160k',
      type: 'Full-time',
      remote: true,
      posted: '2 days ago',
      description: 'We are looking for a senior software engineer to join our growing team...',
      skills: ['React', 'Node.js', 'TypeScript', 'AWS']
    },
    {
      id: 2,
      title: 'Full Stack Developer',
      company: 'StartupXYZ',
      location: 'Austin, TX',
      salary: '$90k - $120k',
      type: 'Full-time',
      remote: false,
      posted: '1 week ago',
      description: 'Join our innovative startup as a full stack developer...',
      skills: ['Vue.js', 'Python', 'PostgreSQL', 'Docker']
    },
    {
      id: 3,
      title: 'Frontend Developer',
      company: 'BigTech Solutions',
      location: 'Remote',
      salary: '$85k - $110k',
      type: 'Contract',
      remote: true,
      posted: '3 days ago',
      description: 'Remote frontend developer position with flexible hours...',
      skills: ['React', 'JavaScript', 'CSS', 'Figma']
    },
    {
      id: 4,
      title: 'DevOps Engineer',
      company: 'CloudServe',
      location: 'Seattle, WA',
      salary: '$100k - $140k',
      type: 'Full-time',
      remote: true,
      posted: '5 days ago',
      description: 'Help us scale our infrastructure and improve deployment processes...',
      skills: ['Kubernetes', 'AWS', 'Terraform', 'Jenkins']
    }
  ]
  
  let filteredJobs = $derived(
    jobs.filter(job => {
      const matchesSearch = !searchQuery || 
        job.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        job.company.toLowerCase().includes(searchQuery.toLowerCase()) ||
        job.skills.some(skill => skill.toLowerCase().includes(searchQuery.toLowerCase()))
      
      const matchesLocation = !locationFilter || 
        job.location.toLowerCase().includes(locationFilter.toLowerCase())
      
      return matchesSearch && matchesLocation
    })
  )
</script>

<svelte:head>
  <title>Job Search - Candidate Portal</title>
</svelte:head>

<div class="space-y-6">
  <!-- Header -->
  <div>
    <h1 class="text-2xl font-bold text-gray-900">Job Search</h1>
    <p class="text-gray-600">Discover opportunities that match your skills and preferences</p>
  </div>

  <!-- Search and Filters -->
  <div class="bg-white rounded-lg border border-gray-200 p-4 sm:p-6">
    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3 sm:gap-4">
      <!-- Search -->
      <div class="relative">
        <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
        <input
          type="text"
          placeholder="Search jobs, companies, skills..."
          bind:value={searchQuery}
          class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
      </div>

        <!-- Location -->
        <div class="relative">
          <MapPin class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Location"
            bind:value={locationFilter}
            class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <!-- Salary -->
        <select
          bind:value={salaryFilter}
          class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="">Any Salary</option>
          <option value="50k">$50k+</option>
          <option value="75k">$75k+</option>
          <option value="100k">$100k+</option>
          <option value="125k">$125k+</option>
        </select>
      </div>
    </div>
  </div>

  <!-- Results -->
  <div class="space-y-4">
    <div class="flex items-center justify-between">
      <p class="text-sm text-gray-600">
        {filteredJobs.length} job{filteredJobs.length !== 1 ? 's' : ''} found
      </p>
      <select class="text-sm border border-gray-300 rounded-md px-3 py-1">
        <option>Most Recent</option>
        <option>Salary: High to Low</option>
        <option>Salary: Low to High</option>
        <option>Company A-Z</option>
      </select>
    </div>

    <!-- Job Cards -->
    <div class="space-y-4">
      {#each filteredJobs as job}
        <div class="bg-white rounded-lg border border-gray-200 p-4 sm:p-6 hover:border-blue-300 transition-colors cursor-pointer">
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <div class="flex items-center gap-3 mb-2">
                <h3 class="text-lg font-semibold text-gray-900">{job.title}</h3>
                {#if job.remote}
                  <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Remote
                  </span>
                {/if}
              </div>
              
              <div class="flex items-center gap-4 text-sm text-gray-600 mb-3">
                <div class="flex items-center gap-1">
                  <Building2 class="w-4 h-4" />
                  {job.company}
                </div>
                <div class="flex items-center gap-1">
                  <MapPin class="w-4 h-4" />
                  {job.location}
                </div>
                <div class="flex items-center gap-1">
                  <DollarSign class="w-4 h-4" />
                  {job.salary}
                </div>
                <div class="flex items-center gap-1">
                  <Clock class="w-4 h-4" />
                  {job.type}
                </div>
              </div>
              
              <p class="text-gray-700 mb-3 line-clamp-2">{job.description}</p>
              
              <div class="flex flex-wrap gap-2 mb-3">
                {#each job.skills as skill}
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    {skill}
                  </span>
                {/each}
              </div>
              
              <p class="text-xs text-gray-500">Posted {job.posted}</p>
            </div>
            
            <div class="ml-4 sm:ml-6 flex flex-col gap-2">
              <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm font-medium">
                Apply Now
              </button>
              <button class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors text-sm font-medium">
                Save Job
              </button>
            </div>
          </div>
        </div>
      {/each}
    </div>

    {#if filteredJobs.length === 0}
      <div class="text-center py-12">
        <Search class="mx-auto h-12 w-12 text-gray-400" />
        <h3 class="mt-2 text-sm font-medium text-gray-900">No jobs found</h3>
        <p class="mt-1 text-sm text-gray-500">Try adjusting your search criteria</p>
      </div>
    {/if}
  </div>
</div>
