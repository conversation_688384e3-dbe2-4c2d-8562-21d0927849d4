<script lang="ts">
  import { goto } from '$app/navigation'
  import TouchButton from '$lib/components/ui/touch-button.svelte'
  import MobileCard from '$lib/components/ui/mobile-card.svelte'
  import MobileInput from '$lib/components/ui/mobile-input.svelte'
  
  let testInput = ''
  let testResults: string[] = []
  
  function addResult(message: string) {
    testResults = [...testResults, `${new Date().toLocaleTimeString()}: ${message}`]
  }
  
  function testNavigation(route: string) {
    addResult(`Testing navigation to ${route}`)
    goto(route)
  }
  
  function testMobileFeatures() {
    addResult('Testing mobile features...')
    
    // Test touch targets
    const buttons = document.querySelectorAll('button')
    let touchTargetIssues = 0
    
    buttons.forEach(button => {
      const rect = button.getBoundingClientRect()
      if (rect.height < 44 || rect.width < 44) {
        touchTargetIssues++
      }
    })
    
    addResult(`Found ${touchTargetIssues} buttons with insufficient touch targets`)
    
    // Test viewport
    const viewport = window.innerWidth
    addResult(`Viewport width: ${viewport}px`)
    
    // Test mobile detection
    const isMobile = viewport < 768
    addResult(`Mobile detected: ${isMobile}`)
  }
</script>

<svelte:head>
  <title>Mobile Test - Candidate Portal</title>
</svelte:head>

<div class="space-y-6 mobile-spacing">
  <div>
    <h1 class="text-2xl font-bold text-gray-900">Mobile Experience Test</h1>
    <p class="text-gray-600">Test mobile-first UI components and navigation</p>
  </div>

  <!-- Navigation Tests -->
  <MobileCard padding="md" class="mobile-card">
    <h2 class="text-lg font-semibold text-gray-900 mb-4">Navigation Tests</h2>
    <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
      <TouchButton variant="primary" on:click={() => testNavigation('/dashboard')}>
        Test Dashboard
      </TouchButton>
      <TouchButton variant="secondary" on:click={() => testNavigation('/jobs')}>
        Test Jobs
      </TouchButton>
      <TouchButton variant="outline" on:click={() => testNavigation('/applications')}>
        Test Applications
      </TouchButton>
      <TouchButton variant="ghost" on:click={() => testNavigation('/profile')}>
        Test Profile
      </TouchButton>
    </div>
  </MobileCard>

  <!-- Mobile Component Tests -->
  <MobileCard padding="md" class="mobile-card">
    <h2 class="text-lg font-semibold text-gray-900 mb-4">Mobile Components</h2>
    <div class="space-y-4">
      <MobileInput
        label="Test Input"
        placeholder="Type something..."
        bind:value={testInput}
      />
      
      <TouchButton 
        variant="primary" 
        class="w-full"
        on:click={testMobileFeatures}
      >
        Run Mobile Tests
      </TouchButton>
    </div>
  </MobileCard>

  <!-- Test Results -->
  <MobileCard padding="md" class="mobile-card">
    <h2 class="text-lg font-semibold text-gray-900 mb-4">Test Results</h2>
    {#if testResults.length === 0}
      <p class="text-gray-500 text-sm">No tests run yet</p>
    {:else}
      <div class="space-y-2 max-h-64 overflow-y-auto">
        {#each testResults as result}
          <div class="text-sm font-mono bg-gray-50 p-2 rounded">
            {result}
          </div>
        {/each}
      </div>
    {/if}
  </MobileCard>

  <!-- Mobile Guidelines -->
  <MobileCard padding="md" class="mobile-card">
    <h2 class="text-lg font-semibold text-gray-900 mb-4">Mobile-First Guidelines</h2>
    <div class="space-y-3 text-sm text-gray-600">
      <div class="flex items-start gap-2">
        <span class="text-green-500">✓</span>
        <span>Touch targets minimum 44px × 44px</span>
      </div>
      <div class="flex items-start gap-2">
        <span class="text-green-500">✓</span>
        <span>Font size 16px+ to prevent zoom on iOS</span>
      </div>
      <div class="flex items-start gap-2">
        <span class="text-green-500">✓</span>
        <span>Bottom navigation for mobile</span>
      </div>
      <div class="flex items-start gap-2">
        <span class="text-green-500">✓</span>
        <span>Responsive breakpoints: 768px, 1024px</span>
      </div>
      <div class="flex items-start gap-2">
        <span class="text-green-500">✓</span>
        <span>Safe area insets for notched devices</span>
      </div>
    </div>
  </MobileCard>
</div>
