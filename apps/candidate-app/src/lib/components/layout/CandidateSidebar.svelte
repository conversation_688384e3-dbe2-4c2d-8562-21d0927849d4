<script lang="ts">
  import { goto } from '$app/navigation'
  import {
    Home,
    Search,
    FileText,
    UserCheck,
    Settings,
    User,
    LogOut,
    ChevronsUpDown
  } from 'lucide-svelte'

  import {
    Sidebar,
    SidebarContent,
    SidebarFooter,
    SidebarGroup,
    SidebarGroupContent,
    SidebarGroupLabel,
    SidebarHeader,
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem,
    useSidebar
  } from '$lib/components/ui/sidebar'



  export let candidateProfile: any = null
  export let pathname: string = '/dashboard'

  // Component state
  let showUserMenu = false

  // Get sidebar context
  let sidebarContext: any = null
  try {
    sidebarContext = useSidebar()
  } catch (e) {
    // Sidebar context not available, that's ok
  }
  
  // Navigation items for candidates
  const navigationItems = [
    {
      section: 'Main',
      items: [
        { name: 'Dashboard', href: '/dashboard', icon: Home },
        { name: 'Job Search', href: '/jobs', icon: Search },
        { name: 'Applications', href: '/applications', icon: FileText },
      ]
    },
    {
      section: 'Account',
      items: [
        { name: 'Profile', href: '/profile', icon: User },
        { name: 'Setting<PERSON>', href: '/settings', icon: Settings },
      ]
    }
  ]
  
  function navigateTo(href: string) {
    goto(href)

    // Close mobile sidebar after navigation
    if (sidebarContext?.isMobile && sidebarContext?.openMobile) {
      sidebarContext.setOpenMobile(false)
    }
  }
  
  async function signOut() {
    const response = await fetch('/api/auth/signout', { method: 'POST' })
    if (response.ok) {
      goto('/')
    }
  }

  // Check if current path is active
  function isActive(href: string): boolean {
    if (href === '/dashboard') {
      return pathname === '/dashboard'
    }
    return pathname.startsWith(href)
  }

  // Get user initials for avatar
  $: userInitials = candidateProfile
    ? `${candidateProfile.first_name?.[0] || ''}${candidateProfile.last_name?.[0] || ''}`.toUpperCase()
    : 'U'

  // Close user menu when clicking outside
  function handleClickOutside(event: MouseEvent) {
    const target = event.target as Element
    if (showUserMenu && !target?.closest('.user-menu-container')) {
      showUserMenu = false
    }
  }
</script>

<!-- Global click handler to close user menu -->
<svelte:window on:click={handleClickOutside} />

<Sidebar collapsible="icon">
  <SidebarHeader>
    <SidebarMenu>
      <SidebarMenuItem>
        <SidebarMenuButton size="lg" class="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground">
          <div class="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
            <UserCheck class="size-4" />
          </div>
          <div class="grid flex-1 text-left text-sm leading-tight">
            <span class="truncate font-semibold">ProcureServe</span>
            <span class="truncate text-xs">Candidate Portal</span>
          </div>
        </SidebarMenuButton>
      </SidebarMenuItem>
    </SidebarMenu>
  </SidebarHeader>

  <SidebarContent>
    {#each navigationItems as section}
      <SidebarGroup>
        <SidebarGroupLabel>{section.section}</SidebarGroupLabel>
        <SidebarGroupContent>
          <SidebarMenu>
            {#each section.items as item}
              <SidebarMenuItem>
                <SidebarMenuButton
                  isActive={isActive(item.href)}
                  on:click={(e) => {
                    e.preventDefault()
                    navigateTo(item.href)
                  }}
                >
                  <svelte:component this={item.icon} />
                  <span>{item.name}</span>
                </SidebarMenuButton>
              </SidebarMenuItem>
            {/each}
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>
    {/each}
  </SidebarContent>

  <SidebarFooter>
    {#if candidateProfile}
      <SidebarMenu>
        <SidebarMenuItem>
          <div class="relative user-menu-container">
            <SidebarMenuButton
              size="lg"
              on:click={() => showUserMenu = !showUserMenu}
              class="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <div class="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
                <span class="text-xs font-semibold">{userInitials}</span>
              </div>
              <div class="grid flex-1 text-left text-sm leading-tight">
                <span class="truncate font-semibold">{candidateProfile.first_name} {candidateProfile.last_name}</span>
                <span class="truncate text-xs">{candidateProfile.email}</span>
              </div>
              <ChevronsUpDown class="ml-auto size-4" />
            </SidebarMenuButton>

            {#if showUserMenu}
              <div class="absolute bottom-full left-0 right-0 mb-2 bg-white border border-gray-200 rounded-lg shadow-lg py-1 z-50">
                <button
                  on:click={() => {
                    navigateTo('/profile')
                    showUserMenu = false
                  }}
                  class="w-full flex items-center gap-2 px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                >
                  <User class="w-4 h-4" />
                  Profile
                </button>
                <button
                  on:click={() => {
                    navigateTo('/settings')
                    showUserMenu = false
                  }}
                  class="w-full flex items-center gap-2 px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                >
                  <Settings class="w-4 h-4" />
                  Settings
                </button>
                <hr class="my-1" />
                <button
                  on:click={() => {
                    signOut()
                    showUserMenu = false
                  }}
                  class="w-full flex items-center gap-2 px-3 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors"
                >
                  <LogOut class="w-4 h-4" />
                  Sign Out
                </button>
              </div>
            {/if}
          </div>
        </SidebarMenuItem>
      </SidebarMenu>
    {/if}
  </SidebarFooter>
</Sidebar>


