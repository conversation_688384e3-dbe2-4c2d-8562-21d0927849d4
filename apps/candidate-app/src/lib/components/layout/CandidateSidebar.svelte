<script lang="ts">
  import { goto } from '$app/navigation'
  import {
    Home,
    Search,
    FileText,
    UserCheck
  } from 'lucide-svelte'
  
  import {
    Sidebar,
    SidebarContent,
    SidebarFooter,
    SidebarGroup,
    SidebarGroupContent,
    SidebarGroupLabel,
    SidebarHeader,
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem,
    useSidebar
  } from '$lib/components/ui/sidebar'
  
  import UserMenu from '$lib/components/ui/user-menu.svelte'
  
  export let candidateProfile: any = null
  export let pathname: string = '/dashboard'

  let isUserMenuOpen = false

  // Get sidebar context for mobile handling
  let sidebarContext: any = null
  try {
    sidebarContext = useSidebar()
  } catch (e) {
    // Sidebar context not available, that's ok
  }
  
  // Navigation items for candidates - removed profile and settings
  const navigationItems = [
    {
      section: 'Main',
      items: [
        { name: 'Dashboard', href: '/dashboard', icon: Home },
        { name: 'Job Search', href: '/jobs', icon: Search },
        { name: 'Applications', href: '/applications', icon: FileText },
      ]
    }
  ]
  
  function navigateTo(href: string) {
    goto(href)

    // Close mobile sidebar after navigation
    if (sidebarContext?.isMobile && sidebarContext?.openMobile) {
      sidebarContext.setOpenMobile(false)
    }
  }
  
  async function signOut() {
    const response = await fetch('/api/auth/signout', { method: 'POST' })
    if (response.ok) {
      goto('/')
    }
  }
  
  // Check if current path is active
  function isActive(href: string): boolean {
    if (href === '/dashboard') {
      return pathname === '/dashboard'
    }
    return pathname.startsWith(href)
  }
  
  function toggleUserMenu() {
    isUserMenuOpen = !isUserMenuOpen
  }
  
  // Get user initials for avatar
  $: userInitials = candidateProfile 
    ? `${candidateProfile.first_name?.[0] || ''}${candidateProfile.last_name?.[0] || ''}`.toUpperCase()
    : 'U'
  
  // Handle user menu events
  function handleUserMenuEvent(event: CustomEvent) {
    const { type, detail } = event
    
    switch(type) {
      case 'signOut':
        signOut()
        break
      case 'profileSettings':
        // TODO: Open profile settings modal/drawer
        console.log('Profile settings clicked')
        break
      case 'dataExport':
        // TODO: Handle data export
        console.log('Data export clicked')
        break
      case 'accountDeletion':
        // TODO: Handle account deletion
        console.log('Account deletion clicked')
        break
      case 'help':
        // TODO: Open help/support
        console.log('Help clicked')
        break
      case 'themeChange':
        // TODO: Handle theme change
        console.log('Theme changed to:', detail)
        break
      case 'languageChange':
        // TODO: Handle language change
        console.log('Language changed to:', detail)
        break
    }
  }
</script>

<Sidebar class="h-screen flex flex-col">
  <SidebarHeader class="flex-shrink-0">
    <div class="flex items-center gap-3 px-3 py-4 border-b border-sidebar-border">
      <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
        <UserCheck class="w-5 h-5 text-white" />
      </div>
      <div class="flex-1 min-w-0">
        <h1 class="font-semibold text-sidebar-foreground text-sm">ProcureServe</h1>
        <p class="text-xs text-sidebar-foreground/70">Candidate Portal</p>
      </div>
    </div>
  </SidebarHeader>

  <SidebarContent class="flex-1 overflow-y-auto py-4">
    {#each navigationItems as section}
      <SidebarGroup class="mb-6">
        <SidebarGroupLabel class="text-xs font-semibold text-sidebar-foreground/70 uppercase tracking-wider px-3 mb-2">
          {section.section}
        </SidebarGroupLabel>
        <SidebarGroupContent>
          <SidebarMenu class="space-y-1 px-2">
            {#each section.items as item}
              <SidebarMenuItem>
                <SidebarMenuButton
                  isActive={isActive(item.href)}
                  on:click={(e) => {
                    e.preventDefault()
                    navigateTo(item.href)
                  }}
                  class="w-full flex items-center gap-3 px-4 py-3 rounded-lg text-sm font-medium transition-all duration-200 hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent/80 {isActive(item.href) ? 'bg-sidebar-accent text-sidebar-accent-foreground shadow-sm' : 'text-sidebar-foreground/80'}"
                >
                  <svelte:component this={item.icon} class="w-4 h-4 flex-shrink-0" />
                  <span class="truncate">{item.name}</span>
                </SidebarMenuButton>
              </SidebarMenuItem>
            {/each}
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>
    {/each}
  </SidebarContent>

  <SidebarFooter class="flex-shrink-0 relative">
    {#if candidateProfile}
      <!-- User Menu Trigger -->
      <div class="px-2 pb-4">
        <button
          on:click={toggleUserMenu}
          class="w-full flex items-center gap-3 px-3 py-3 rounded-lg hover:bg-sidebar-accent transition-colors text-left group"
          aria-label="Open user menu"
        >
          <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white font-semibold text-xs flex-shrink-0">
            {userInitials}
          </div>
          <div class="flex-1 min-w-0">
            <p class="text-sm font-medium text-sidebar-foreground truncate">
              {candidateProfile.first_name} {candidateProfile.last_name}
            </p>
            <p class="text-xs text-sidebar-foreground/60 truncate">
              {candidateProfile.email}
            </p>
          </div>
          <div class="flex-shrink-0 text-sidebar-foreground/40 group-hover:text-sidebar-foreground/60 transition-colors">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l4-4 4 4m0 6l-4 4-4-4"></path>
            </svg>
          </div>
        </button>
      </div>
      
      <!-- User Menu Component -->
      <UserMenu 
        {candidateProfile} 
        bind:isOpen={isUserMenuOpen}
        on:close={() => isUserMenuOpen = false}
        on:signOut={handleUserMenuEvent}
        on:profileSettings={handleUserMenuEvent}
        on:dataExport={handleUserMenuEvent}
        on:accountDeletion={handleUserMenuEvent}
        on:help={handleUserMenuEvent}
        on:themeChange={handleUserMenuEvent}
        on:languageChange={handleUserMenuEvent}
      />
    {/if}
  </SidebarFooter>
</Sidebar>

<style>
  :global(.sidebar) {
    height: 100vh;
    display: flex;
    flex-direction: column;
  }
  
  /* Mobile responsive adjustments - improved for better mobile experience */
  @media (max-width: 767px) {
    :global(.sidebar) {
      width: 85vw !important;
      max-width: 320px;
      height: 100vh;
    }

    /* Improve touch targets on mobile */
    :global(.sidebar button) {
      min-height: 48px;
      padding: 12px 16px;
    }

    /* Better mobile spacing */
    :global(.sidebar .space-y-1 > * + *) {
      margin-top: 0.5rem;
    }
  }

  /* Tablet adjustments */
  @media (min-width: 768px) and (max-width: 1024px) {
    :global(.sidebar) {
      width: 280px;
    }
  }

  /* Desktop */
  @media (min-width: 1025px) {
    :global(.sidebar) {
      width: 320px;
    }
  }
  
  /* Custom scrollbar for sidebar content */
  :global(.sidebar .overflow-y-auto) {
    scrollbar-width: thin;
    scrollbar-color: rgba(155, 155, 155, 0.3) transparent;
  }
  
  :global(.sidebar .overflow-y-auto::-webkit-scrollbar) {
    width: 4px;
  }
  
  :global(.sidebar .overflow-y-auto::-webkit-scrollbar-track) {
    background: transparent;
  }
  
  :global(.sidebar .overflow-y-auto::-webkit-scrollbar-thumb) {
    background-color: rgba(155, 155, 155, 0.3);
    border-radius: 2px;
  }
  
  :global(.sidebar .overflow-y-auto::-webkit-scrollbar-thumb:hover) {
    background-color: rgba(155, 155, 155, 0.5);
  }
</style>
