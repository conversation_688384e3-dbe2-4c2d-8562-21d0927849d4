<script lang="ts">
  import { Bell, Menu } from 'lucide-svelte'
  import { onMount } from 'svelte'

  import { SidebarProvider, SidebarTrigger } from '$lib/components/ui/sidebar'
  import CandidateSidebar from './CandidateSidebar.svelte'

  export let candidateProfile: any = null
  export let pageTitle: string = 'Dashboard'
  export let pathname: string = '/dashboard'

  let isMobile = false
  let isTablet = false
  let defaultOpen = true

  onMount(() => {
    const checkScreenSize = () => {
      const width = window.innerWidth
      isMobile = width < 768  // Increased mobile breakpoint for better mobile experience
      isTablet = width >= 768 && width < 1024
      defaultOpen = width >= 1024 // Only default open on desktop
    }

    checkScreenSize()
    window.addEventListener('resize', checkScreenSize)

    return () => {
      window.removeEventListener('resize', checkScreenSize)
    }
  })

  // Generate clean page titles
  function getCleanPageTitle(title: string): string {
    return title.charAt(0).toUpperCase() + title.slice(1)
  }
</script>

<SidebarProvider defaultOpen={defaultOpen}>
  <div class="min-h-screen w-full bg-gray-50 dark:bg-gray-950 flex">
    <!-- Sidebar -->
    <CandidateSidebar {candidateProfile} {pathname} />
    
    <!-- Main Content Area -->
    <div class="flex-1 flex flex-col min-h-screen overflow-hidden">
      <!-- Top Navigation Header -->
      <header class="sticky top-0 z-40 w-full border-b border-gray-200 dark:border-gray-800 bg-white/95 dark:bg-gray-900/95 backdrop-blur supports-[backdrop-filter]:bg-white/60 dark:supports-[backdrop-filter]:bg-gray-900/60">
        <div class="flex h-16 items-center gap-3 px-4 sm:px-6 lg:px-8">
          <!-- Mobile menu trigger -->
          <SidebarTrigger class="lg:hidden p-2 -ml-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
            <Menu class="h-6 w-6" />
            <span class="sr-only">Toggle sidebar</span>
          </SidebarTrigger>

          <!-- Page title -->
          <div class="flex-1 min-w-0">
            <h1 class="text-lg font-semibold text-gray-900 dark:text-white lg:text-xl truncate">
              {getCleanPageTitle(pageTitle)}
            </h1>
          </div>

          <!-- Header actions -->
          <div class="flex items-center gap-2">
            <!-- Notifications -->
            <button
              class="relative inline-flex items-center justify-center rounded-lg text-sm font-medium transition-colors hover:bg-gray-100 dark:hover:bg-gray-800 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-9 w-9"
              title="Notifications"
            >
              <Bell class="h-4 w-4 text-gray-600 dark:text-gray-300" />
              <!-- Notification badge -->
              <span class="absolute -top-1 -right-1 h-2 w-2 bg-red-500 rounded-full"></span>
              <span class="sr-only">Notifications</span>
            </button>
          </div>
        </div>
      </header>

      <!-- Main content -->
      <main class="flex-1 overflow-auto">
        <!-- Content container with mobile-optimized padding -->
        <div class="h-full">
          <div class="mx-auto max-w-7xl px-3 py-4 sm:px-6 sm:py-6 lg:px-8">
            <slot />
          </div>
        </div>
      </main>
    </div>
  </div>
</SidebarProvider>

<style>
  /* Global mobile-first styles */
  :global(body) {
    font-size: 14px;
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Mobile styles - improved touch targets and spacing */
  @media (max-width: 767px) {
    :global(.sidebar-trigger) {
      display: block !important;
    }

    /* Improve touch targets on mobile */
    :global(button) {
      min-height: 44px;
      min-width: 44px;
    }

    /* Better mobile spacing */
    :global(.space-y-6 > * + *) {
      margin-top: 1rem !important;
    }

    :global(.space-y-4 > * + *) {
      margin-top: 0.75rem !important;
    }
  }

  /* Tablet styles */
  @media (min-width: 768px) and (max-width: 1023px) {
    :global(body) {
      font-size: 15px;
    }
  }

  /* Desktop styles */
  @media (min-width: 1024px) {
    :global(body) {
      font-size: 16px;
    }

    :global(.sidebar-trigger) {
      display: none !important;
    }
  }
  
  /* Improve scrollbar styling */
  :global(.overflow-auto) {
    scrollbar-width: thin;
    scrollbar-color: rgba(156, 163, 175, 0.3) transparent;
  }
  
  :global(.overflow-auto::-webkit-scrollbar) {
    width: 6px;
  }
  
  :global(.overflow-auto::-webkit-scrollbar-track) {
    background: transparent;
  }
  
  :global(.overflow-auto::-webkit-scrollbar-thumb) {
    background-color: rgba(156, 163, 175, 0.3);
    border-radius: 3px;
  }
  
  :global(.overflow-auto::-webkit-scrollbar-thumb:hover) {
    background-color: rgba(156, 163, 175, 0.5);
  }
  
  /* Enterprise-grade typography improvements */
  :global(h1) {
    font-weight: 600;
    letter-spacing: -0.025em;
  }
  
  :global(h2) {
    font-weight: 600;
    letter-spacing: -0.025em;
  }
  
  :global(h3) {
    font-weight: 600;
    letter-spacing: -0.025em;
  }
  
  /* Improved button styling */
  :global(.btn) {
    font-weight: 500;
    transition: all 0.2s ease-in-out;
  }
  
  /* Card improvements */
  :global(.card) {
    border-radius: 12px;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  }
  
  /* Focus improvements for accessibility */
  :global(*:focus-visible) {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
  }
  
  /* Loading states */
  :global(.loading) {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
  
  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }
</style>
