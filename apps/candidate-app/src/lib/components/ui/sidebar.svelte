<script lang="ts" context="module">
  import { writable } from 'svelte/store'
  import { getContext } from 'svelte'

  const SIDEBAR_COOKIE_NAME = "sidebar_state"
  const SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 365 // 1 year
  const SIDEBAR_WIDTH = "16rem"
  const SIDEBAR_WIDTH_MOBILE = "18rem"
  const SIDEBAR_WIDTH_ICON = "3rem"
  const SIDEBAR_KEYBOARD_SHORTCUT = "b"

  interface SidebarContext {
    state: 'expanded' | 'collapsed'
    open: boolean
    setOpen: (open: boolean | ((value: boolean) => boolean)) => void
    openMobile: boolean
    setOpenMobile: (open: boolean) => void
    isMobile: boolean
    toggleSidebar: () => void
  }

  const SIDEBAR_CONTEXT_KEY = 'sidebar'

  export function createSidebarContext(defaultOpen = true) {
    const open = writable(defaultOpen)
    const openMobile = writable(false)
    const isMobile = writable(false)

    let openValue = defaultOpen
    let openMobileValue = false
    let isMobileValue = false

    open.subscribe(v => openValue = v)
    openMobile.subscribe(v => openMobileValue = v)
    isMobile.subscribe(v => isMobileValue = v)

    const context: SidebarContext = {
      get state(): 'expanded' | 'collapsed' {
        return openValue ? 'expanded' : 'collapsed'
      },
      get open() {
        return openValue
      },
      setOpen: (value: boolean | ((value: boolean) => boolean)) => {
        const openState = typeof value === "function" ? value(openValue) : value
        open.set(openState)
        // Set cookie to persist state
        if (typeof document !== 'undefined') {
          document.cookie = `${SIDEBAR_COOKIE_NAME}=${openState}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`
        }
      },
      get openMobile() {
        return openMobileValue
      },
      setOpenMobile: (value: boolean) => {
        openMobile.set(value)
      },
      get isMobile() {
        return isMobileValue
      },
      set isMobile(value: boolean) {
        isMobile.set(value)
      },
      toggleSidebar: () => {
        if (isMobileValue) {
          context.setOpenMobile(!openMobileValue)
        } else {
          context.setOpen(!openValue)
        }
      }
    }

    return context
  }

  export function useSidebar(): SidebarContext {
    const context = getContext<SidebarContext>(SIDEBAR_CONTEXT_KEY)
    if (!context) {
      throw new Error("useSidebar must be used within a SidebarProvider.")
    }
    return context
  }
</script>

<script lang="ts">
  import { cn } from '$lib/utils'
  import { onMount } from 'svelte'

  let className: string = ''
  export { className as class }
  export let side: 'left' | 'right' = 'left'
  export let variant: 'sidebar' | 'floating' | 'inset' = 'sidebar'
  export let collapsible: 'offcanvas' | 'icon' | 'none' = 'offcanvas'

  let context: SidebarContext | null = null

  try {
    context = useSidebar()
  } catch (e) {
    // Sidebar not in provider context, that's ok
  }

  onMount(() => {
    if (!context) return

    // Handle keyboard shortcut
    const handleKeydown = (e: KeyboardEvent) => {
      if (e.key === SIDEBAR_KEYBOARD_SHORTCUT && (e.metaKey || e.ctrlKey)) {
        e.preventDefault()
        context?.toggleSidebar()
      }
    }

    // Check if mobile and update context
    const checkMobile = () => {
      const isMobileNow = window.innerWidth < 1024
      if (context) {
        // Update mobile state in context
        context.isMobile = isMobileNow
        // Close mobile sidebar when switching to desktop
        if (!isMobileNow && context.openMobile) {
          context.setOpenMobile(false)
        }
      }
    }

    window.addEventListener('keydown', handleKeydown)
    window.addEventListener('resize', checkMobile)
    checkMobile()

    return () => {
      window.removeEventListener('keydown', handleKeydown)
      window.removeEventListener('resize', checkMobile)
    }
  })

  // Determine if sidebar should be collapsed
  $: isCollapsed = collapsible === 'icon' && !context?.isMobile && !context?.open
</script>

<!-- Mobile overlay -->
{#if context?.isMobile && context?.openMobile}
  <div
    class="fixed inset-0 z-40 bg-black/50 lg:hidden"
    on:click={() => context?.setOpenMobile(false)}
    on:keydown={(e) => e.key === 'Escape' && context?.setOpenMobile(false)}
    role="button"
    tabindex="0"
  ></div>
{/if}

{#if collapsible === 'none'}
  <div
    class={cn(
      'flex h-full w-[--sidebar-width] flex-col bg-sidebar text-sidebar-foreground',
      className
    )}
    style="--sidebar-width: {SIDEBAR_WIDTH}; --sidebar-width-mobile: {SIDEBAR_WIDTH_MOBILE}; --sidebar-width-icon: {SIDEBAR_WIDTH_ICON};"
    {...$$restProps}
  >
    <slot />
  </div>
{:else}
  <aside
    class={cn(
      'group peer hidden md:block text-sidebar-foreground',
      variant === 'floating' && 'p-2',
      variant === 'inset' && 'bg-background',
      className
    )}
    style="--sidebar-width: {SIDEBAR_WIDTH}; --sidebar-width-mobile: {SIDEBAR_WIDTH_MOBILE}; --sidebar-width-icon: {SIDEBAR_WIDTH_ICON};"
    data-state={context?.open ? 'expanded' : 'collapsed'}
    data-collapsible={context?.state === 'collapsed' ? collapsible : ''}
    data-variant={variant}
    data-side={side}
    {...$$restProps}
  >
    <!-- Desktop Sidebar -->
    <div
      class={cn(
        'duration-200 relative h-svh w-[--sidebar-width] bg-sidebar transition-[width] ease-linear',
        'group-data-[collapsible=offcanvas]:w-0',
        'group-data-[side=right]:rotate-180',
        variant === 'floating' && 'rounded-lg border border-sidebar-border shadow',
        variant === 'inset' && 'rounded-lg border border-sidebar-border shadow-sm'
      )}
    >
      <div
        class={cn(
          'flex h-full w-full flex-col',
          'group-data-[collapsible=icon]:w-[--sidebar-width-icon]',
          'group-data-[side=right]:rotate-180'
        )}
      >
        <slot />
      </div>
    </div>
  </aside>

  <!-- Mobile Sidebar -->
  <aside
    class={cn(
      'fixed inset-y-0 z-50 flex h-svh w-[--sidebar-width-mobile] flex-col bg-sidebar text-sidebar-foreground transition-transform duration-300 ease-in-out md:hidden',
      side === 'left' && (context?.openMobile ? 'translate-x-0' : '-translate-x-full'),
      side === 'right' && (context?.openMobile ? 'translate-x-0' : 'translate-x-full'),
      side === 'right' && 'right-0',
      variant === 'floating' && 'p-2',
      variant === 'inset' && 'bg-background'
    )}
    style="--sidebar-width: {SIDEBAR_WIDTH}; --sidebar-width-mobile: {SIDEBAR_WIDTH_MOBILE}; --sidebar-width-icon: {SIDEBAR_WIDTH_ICON};"
    data-state={context?.openMobile ? 'expanded' : 'collapsed'}
    data-mobile="true"
    data-variant={variant}
    data-side={side}
  >
    <div
      class={cn(
        'flex h-full w-full flex-col',
        variant === 'floating' && 'rounded-lg border border-sidebar-border shadow',
        variant === 'inset' && 'rounded-lg border border-sidebar-border shadow-sm'
      )}
    >
      <slot />
    </div>
  </aside>
{/if}
