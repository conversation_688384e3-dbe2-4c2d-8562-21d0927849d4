<script lang="ts">
  import { page } from '$app/stores'
  import { goto } from '$app/navigation'
  import { Home, Search, FileText, User } from 'lucide-svelte'
  
  // Navigation items for mobile bottom nav
  const navigationItems = [
    { name: 'Dashboard', href: '/dashboard', icon: Home },
    { name: 'Jobs', href: '/jobs', icon: Search },
    { name: 'Applications', href: '/applications', icon: FileText },
    { name: 'Profile', href: '/profile', icon: User },
  ]
  
  function navigateTo(href: string) {
    goto(href)
  }
  
  // Check if current path is active
  function isActive(href: string): boolean {
    if (href === '/dashboard') {
      return $page.url.pathname === '/dashboard'
    }
    return $page.url.pathname.startsWith(href)
  }
</script>

<!-- Mobile Bottom Navigation -->
<nav class="fixed bottom-0 left-0 right-0 z-50 bg-white border-t border-gray-200 dark:bg-gray-900 dark:border-gray-800 md:hidden">
  <div class="grid grid-cols-4 h-16">
    {#each navigationItems as item}
      <button
        on:click={() => navigateTo(item.href)}
        class="flex flex-col items-center justify-center gap-1 px-2 py-2 text-xs font-medium transition-colors {isActive(item.href) 
          ? 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20' 
          : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-800'}"
        aria-label={item.name}
      >
        <svelte:component this={item.icon} class="w-5 h-5" />
        <span class="truncate">{item.name}</span>
      </button>
    {/each}
  </div>
</nav>

<style>
  /* Ensure content doesn't get hidden behind bottom nav on mobile */
  :global(body) {
    padding-bottom: env(safe-area-inset-bottom);
  }
  
  @media (max-width: 767px) {
    :global(.main-content) {
      padding-bottom: 4rem; /* 64px for bottom nav */
    }
  }
</style>
