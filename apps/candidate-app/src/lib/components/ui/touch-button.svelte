<script lang="ts">
  import { cn } from '$lib/utils'
  
  let className: string = ''
  export { className as class }
  export let variant: 'primary' | 'secondary' | 'outline' | 'ghost' = 'primary'
  export let size: 'sm' | 'md' | 'lg' = 'md'
  export let disabled = false
  export let type: 'button' | 'submit' | 'reset' = 'button'
  
  const variants = {
    primary: 'bg-blue-600 text-white hover:bg-blue-700 active:bg-blue-800 focus:ring-blue-500',
    secondary: 'bg-gray-600 text-white hover:bg-gray-700 active:bg-gray-800 focus:ring-gray-500',
    outline: 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 active:bg-gray-100 focus:ring-blue-500',
    ghost: 'text-gray-700 hover:bg-gray-100 active:bg-gray-200 focus:ring-gray-500'
  }
  
  const sizes = {
    sm: 'px-3 py-2 text-sm min-h-[40px]',
    md: 'px-4 py-3 text-sm min-h-[44px]',
    lg: 'px-6 py-4 text-base min-h-[48px]'
  }
</script>

<button
  {type}
  {disabled}
  class={cn(
    'inline-flex items-center justify-center gap-2 rounded-lg font-medium transition-all duration-200',
    'focus:outline-none focus:ring-2 focus:ring-offset-2',
    'disabled:opacity-50 disabled:pointer-events-none',
    'touch-manipulation', // Improves touch responsiveness
    'select-none', // Prevents text selection on touch
    variants[variant],
    sizes[size],
    className
  )}
  on:click
  {...$$restProps}
>
  <slot />
</button>

<style>
  /* Enhanced touch targets for mobile */
  button {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
  
  /* Ensure minimum touch target size on mobile */
  @media (max-width: 767px) {
    button {
      min-width: 44px;
      min-height: 44px;
    }
  }
  
  /* Active state for better touch feedback */
  button:active {
    transform: scale(0.98);
  }
</style>
