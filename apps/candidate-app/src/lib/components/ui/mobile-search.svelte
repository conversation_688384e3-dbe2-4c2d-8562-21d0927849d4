<script lang="ts">
  import { Search, X, Filter } from 'lucide-svelte'
  import { cn } from '$lib/utils'
  
  export let searchQuery = ''
  export let placeholder = 'Search...'
  export let showFilters = false
  export let filterCount = 0
  
  let searchInput: HTMLInputElement
  let isExpanded = false
  
  function toggleExpanded() {
    isExpanded = !isExpanded
    if (isExpanded) {
      setTimeout(() => searchInput?.focus(), 100)
    }
  }
  
  function clearSearch() {
    searchQuery = ''
    searchInput?.focus()
  }
  
  function handleKeydown(e: KeyboardEvent) {
    if (e.key === 'Escape') {
      isExpanded = false
      searchInput?.blur()
    }
  }
</script>

<div class="relative">
  <!-- Mobile Search Bar -->
  <div class="md:hidden">
    {#if !isExpanded}
      <!-- Collapsed Search Button -->
      <button
        on:click={toggleExpanded}
        class="w-full flex items-center justify-between p-4 bg-white border border-gray-200 rounded-lg shadow-sm"
      >
        <div class="flex items-center gap-3 text-gray-500">
          <Search class="w-5 h-5" />
          <span class="text-sm">{searchQuery || placeholder}</span>
        </div>
        {#if showFilters}
          <div class="flex items-center gap-2">
            {#if filterCount > 0}
              <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                {filterCount}
              </span>
            {/if}
            <Filter class="w-4 h-4 text-gray-400" />
          </div>
        {/if}
      </button>
    {:else}
      <!-- Expanded Search Input -->
      <div class="bg-white border border-gray-200 rounded-lg shadow-lg p-4 space-y-4">
        <div class="flex items-center gap-3">
          <div class="relative flex-1">
            <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              bind:this={searchInput}
              bind:value={searchQuery}
              type="text"
              {placeholder}
              class="w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base"
              on:keydown={handleKeydown}
            />
            {#if searchQuery}
              <button
                on:click={clearSearch}
                class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                <X class="w-5 h-5" />
              </button>
            {/if}
          </div>
          <button
            on:click={toggleExpanded}
            class="px-4 py-3 text-sm font-medium text-gray-600 hover:text-gray-800"
          >
            Cancel
          </button>
        </div>
        
        {#if showFilters}
          <div class="border-t pt-4">
            <slot name="filters" />
          </div>
        {/if}
      </div>
    {/if}
  </div>
  
  <!-- Desktop Search Bar -->
  <div class="hidden md:block">
    <div class="relative">
      <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
      <input
        bind:value={searchQuery}
        type="text"
        {placeholder}
        class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
      />
    </div>
  </div>
</div>

<style>
  /* Prevent zoom on iOS */
  input {
    font-size: 16px;
  }
  
  /* Smooth transitions */
  .space-y-4 > * + * {
    margin-top: 1rem;
  }
</style>
