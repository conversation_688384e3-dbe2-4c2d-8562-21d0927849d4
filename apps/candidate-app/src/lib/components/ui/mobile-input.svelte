<script lang="ts">
  import { cn } from '$lib/utils'
  
  let className: string = ''
  export { className as class }
  export let type: string = 'text'
  export let placeholder: string = ''
  export let value: string = ''
  export let disabled = false
  export let required = false
  export let label: string = ''
  export let error: string = ''
  export let id: string = ''
  
  // Generate unique ID if not provided
  if (!id) {
    id = `input-${Math.random().toString(36).substr(2, 9)}`
  }
</script>

<div class="space-y-2">
  {#if label}
    <label 
      for={id} 
      class="block text-sm font-medium text-gray-700 dark:text-gray-300"
    >
      {label}
      {#if required}
        <span class="text-red-500 ml-1">*</span>
      {/if}
    </label>
  {/if}
  
  <input
    {id}
    {type}
    {placeholder}
    {disabled}
    {required}
    bind:value
    class={cn(
      'w-full rounded-lg border border-gray-300 dark:border-gray-600',
      'bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100',
      'px-4 py-3 text-base', // Larger padding and text for mobile
      'placeholder:text-gray-500 dark:placeholder:text-gray-400',
      'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
      'disabled:opacity-50 disabled:cursor-not-allowed',
      'transition-colors duration-200',
      error && 'border-red-500 focus:ring-red-500 focus:border-red-500',
      className
    )}
    on:input
    on:change
    on:focus
    on:blur
    {...$$restProps}
  />
  
  {#if error}
    <p class="text-sm text-red-600 dark:text-red-400 mt-1">
      {error}
    </p>
  {/if}
</div>

<style>
  /* Mobile-optimized input styling */
  input {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    font-size: 16px; /* Prevents zoom on iOS */
  }
  
  /* Better mobile focus states */
  @media (max-width: 767px) {
    input {
      min-height: 48px; /* Larger touch target */
      font-size: 16px; /* Prevents iOS zoom */
    }
    
    input:focus {
      transform: none; /* Prevent layout shift on focus */
    }
  }
  
  /* Improve autofill styling */
  input:-webkit-autofill,
  input:-webkit-autofill:hover,
  input:-webkit-autofill:focus {
    -webkit-box-shadow: 0 0 0 1000px white inset;
    -webkit-text-fill-color: #111827;
    transition: background-color 5000s ease-in-out 0s;
  }
  
  /* Dark mode autofill */
  @media (prefers-color-scheme: dark) {
    input:-webkit-autofill,
    input:-webkit-autofill:hover,
    input:-webkit-autofill:focus {
      -webkit-box-shadow: 0 0 0 1000px #1f2937 inset;
      -webkit-text-fill-color: #f9fafb;
    }
  }
</style>
