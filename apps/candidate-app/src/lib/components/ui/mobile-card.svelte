<script lang="ts">
  import { cn } from '$lib/utils'
  
  let className: string = ''
  export { className as class }
  export let variant: 'default' | 'interactive' | 'elevated' = 'default'
  export let padding: 'sm' | 'md' | 'lg' = 'md'
  export let clickable = false
  
  const variants = {
    default: 'bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800',
    interactive: 'bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 hover:border-blue-300 dark:hover:border-blue-600 hover:shadow-sm cursor-pointer transition-all duration-200',
    elevated: 'bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 shadow-sm hover:shadow-md transition-shadow duration-200'
  }
  
  const paddings = {
    sm: 'p-3 sm:p-4',
    md: 'p-4 sm:p-6',
    lg: 'p-6 sm:p-8'
  }
</script>

{#if clickable}
  <button
    class={cn(
      'w-full text-left rounded-xl',
      'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
      'active:scale-[0.99] transition-transform duration-100',
      variants[variant],
      paddings[padding],
      className
    )}
    on:click
    {...$$restProps}
  >
    <slot />
  </button>
{:else}
  <div
    class={cn(
      'rounded-xl',
      variants[variant],
      paddings[padding],
      className
    )}
    {...$$restProps}
  >
    <slot />
  </div>
{/if}

<style>
  /* Enhanced mobile touch experience */
  button {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
  }
  
  /* Better mobile spacing */
  @media (max-width: 767px) {
    :global(.mobile-card .space-y-4 > * + *) {
      margin-top: 0.75rem;
    }
    
    :global(.mobile-card .space-y-3 > * + *) {
      margin-top: 0.5rem;
    }
    
    :global(.mobile-card .gap-4) {
      gap: 0.75rem;
    }
    
    :global(.mobile-card .gap-3) {
      gap: 0.5rem;
    }
  }
</style>
