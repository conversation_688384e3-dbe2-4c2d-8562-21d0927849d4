@tailwind base;
@tailwind components;
@tailwind utilities;

/* Mobile-First Candidate App Styles */
@layer base {
  /* Improve mobile font rendering */
  html {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }

  /* Mobile-first body styles */
  body {
    font-size: 14px;
    line-height: 1.5;
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
  }

  /* Prevent zoom on form inputs on iOS */
  input, select, textarea {
    font-size: 16px;
  }

  /* Better mobile scrolling */
  * {
    -webkit-overflow-scrolling: touch;
  }
}

@layer components {
  /* Mobile-optimized touch targets */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Mobile-friendly buttons */
  .mobile-button {
    @apply touch-target px-4 py-3 rounded-lg font-medium transition-all duration-200;
    @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
    @apply active:scale-95 select-none;
    -webkit-tap-highlight-color: transparent;
  }

  /* Mobile-optimized cards */
  .mobile-card {
    @apply rounded-xl border transition-all duration-200;
    @apply hover:shadow-sm active:scale-[0.99];
  }

  /* Mobile-friendly form inputs */
  .mobile-input {
    @apply w-full px-4 py-3 rounded-lg border;
    @apply focus:outline-none focus:ring-2 focus:ring-blue-500;
    @apply transition-colors duration-200;
    font-size: 16px; /* Prevents zoom on iOS */
  }
}

@layer utilities {
  /* Mobile spacing utilities */
  .mobile-spacing {
    @apply space-y-4;
  }

  .mobile-spacing > * + * {
    margin-top: 1rem;
  }

  /* Safe area utilities for mobile */
  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-area-left {
    padding-left: env(safe-area-inset-left);
  }

  .safe-area-right {
    padding-right: env(safe-area-inset-right);
  }
}

/* Responsive Design Improvements */
@media (max-width: 767px) {
  /* Mobile-specific improvements */
  .grid-cols-2 {
    @apply grid-cols-1;
  }

  .grid-cols-3 {
    @apply grid-cols-1;
  }

  .grid-cols-4 {
    @apply grid-cols-2;
  }

  /* Better mobile text sizing */
  h1 {
    @apply text-xl;
  }

  h2 {
    @apply text-lg;
  }

  h3 {
    @apply text-base;
  }

  /* Mobile-optimized spacing */
  .space-y-6 > * + * {
    margin-top: 1rem;
  }

  .space-y-4 > * + * {
    margin-top: 0.75rem;
  }

  .gap-6 {
    gap: 1rem;
  }

  .gap-4 {
    gap: 0.75rem;
  }

  /* Mobile padding adjustments */
  .p-6 {
    @apply p-4;
  }

  .px-6 {
    @apply px-4;
  }

  .py-6 {
    @apply py-4;
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  /* Tablet-specific improvements */
  body {
    font-size: 15px;
  }
}

@media (min-width: 1024px) {
  /* Desktop improvements */
  body {
    font-size: 16px;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --radius: 0.5rem;

    /* Sidebar variables */
    --sidebar: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --sidebar-background: 0 0% 98%;
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;

    /* Sidebar variables for dark mode */
    --sidebar: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
  }
}

@layer base {
  body {
    @apply bg-background text-foreground;
  }
}



@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
